# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the Aethelus AuthService - a Rust-based authentication and authorization service following Domain-Driven Design (DDD) principles with hexagonal architecture. The project is currently in the planning phase with comprehensive documentation but no implementation yet.

## Critical Requirements

### Rust Configuration
- **Rust Edition**: 2024 (MANDATORY - do not change)
- **Workspace Resolver**: 3 (MANDATORY - do not change)
- **Dependency Versions**: NEVER change dependency versions in Cargo.toml (MANDATORY - if API issues arise, check documentation instead of changing versions)
- **No Prometheus**: This project does not use Prometheus

### Architecture Rules
- Follow Domain-Driven Design with hexagonal architecture
- Domain crate cannot depend on OTHER PROJECT LAYERS (application, infrastructure, server)
- Domain crate may use external crates from crates.io as needed for business logic
- Use static dispatch via generics instead of dyn traits
- Separate domain models from persistence records
- All code must be developed using Test-Driven Development (TDD)

### Development Workflow
1. **Requirements Audit**: Extract all mandates from docs before coding
2. **User Validation**: Present understanding for approval before implementation
3. **TDD Cycle**: Write failing tests first → implement → refactor
4. **Quality Gates**: Run clippy, fmt, and audit before considering complete
5. **Code Review**: Submit to code-review agent → iterate on feedback → achieve approval
6. **Task Handoff**: Document review scores and approval in handoff notes

### Code Review Integration

After completing implementation and passing local quality checks, all code must be reviewed by the code-review agent before task completion. The review process follows these steps:

1. **Submit for Review**: After committing changes, invoke the code-review agent with context:
   ```
   "Review implementation of [task description] focusing on [specific concerns]"
   ```

2. **Review Scoring**: The code-review agent evaluates based on weighted criteria:
   - Security: 40% (must be ≥90%)
   - Testing: 25%
   - Performance: 20%
   - Quality: 10%
   - Documentation: 5%
   - Overall score must be ≥95% for approval

3. **Iteration Process**:
   - If not approved: Address all CRITICAL and HIGH PRIORITY feedback
   - Re-run quality checks after changes
   - Re-submit for review with note about addressed feedback
   - Continue until approval achieved

4. **Handoff Documentation**: Include final review scores in task completion:
   ```markdown
   ## Task Completion
   - Implementation: ✓ Complete
   - Tests: ✓ 98% coverage
   - Code Review: ✓ Approved
     - Security: 95/100
     - Testing: 96/100
     - Performance: 100/100
     - Quality: 98/100
     - Overall: 96.35%
   ```

## Build and Development Commands

```bash
# Build the workspace
cargo build --workspace

# Run all tests
cargo test --workspace

# Run specific test categories
cargo test --lib                    # Unit tests only
cargo test tests/integration        # Mock integration tests
cargo test tests/live_integration   # Live integration tests with real DB
cargo test tests/e2e               # End-to-end tests

# Code quality checks
cargo fmt --all -- --check         # Check formatting
cargo clippy --all-targets --all-features -- -D warnings
cargo audit --ignore RUSTSEC-2023-0071  # Security audit (ignore known MySQL/RSA issue)

# Coverage
cargo llvm-cov --workspace --html  # Generate coverage report

# Run a single test
cargo test test_name -- --exact

# Run tests in a specific module
cargo test module::path::to::tests

# Benchmarks
cargo bench

# Database migrations (when implemented)
sqlx migrate run
```

## Project Structure

The project uses a Cargo workspace with clear separation of concerns:

- `domain/` - Pure business logic, independent of other project layers
- `application/` - Use cases and command/query handlers
- `infrastructure/` - External adapters (DB, cache, email, etc.)
- `server/` - API layers (REST/gRPC) and main entry point
- `tests/` - Integration and E2E tests organized by type

## Key Development Principles

1. **Safety First**: No unwrap/expect in production code, explicit error handling
2. **Performance**: Use arrays over Vec for known sizes, static dispatch
3. **Testing**: 100% unit test coverage for functions, comprehensive integration tests
4. **Security**: Include security tests in every category, constant-time operations for crypto
5. **Idiomatic Rust**: Leverage ownership/borrowing, prefer iterators, minimize unsafe

## Testing Standards

- **Unit Tests**: In-file under `#[cfg(test)]`, 100% function coverage
- **Mock Integration**: Fast tests with mockall for component interactions
- **Live Integration**: Real DB/server tests using testcontainers
- **E2E Tests**: Browser automation for user flows (OAuth, SAML)

## Dependencies

All dependencies are pinned in `Docs/NewPlans/Pinned-Versions.md`. Key crates:
- sqlx 0.8.6 for database
- tokio 1.47.0 for async runtime
- figment 0.10.19 for configuration
- argon2 0.6.0-rc.0 for password hashing
- tonic 0.14.0 for gRPC

## Security Considerations

- Authentication service requires exceptional security focus
- All auth operations must complete in <100ms
- Include timing attack prevention
- Implement rate limiting and threat detection
- Use WebAuthn, MFA (TOTP/SMS/email), and passwordless flows

## Common Tasks

When implementing features:
1. Review relevant documentation in `Docs/NewPlans/`
2. Follow the architecture in `Architectural-Standards.md`
3. Apply rules from `AgentRules.md` and `SecurityRules.md`
4. Use TDD workflow from `TestingStandards.md`
5. Implement features from `Auth-Features-List.md` in priority order