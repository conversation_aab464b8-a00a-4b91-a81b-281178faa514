// User authentication command and handler
// Handles the use case of authenticating a user

use crate::services::ApplicationError;
use auth_domain::{
    events::user_logged_in::{AuthenticationMethod, UserLoggedIn},
    repositories::{SessionRepository, UserRepository},
    services::AuthService,
    value_objects::{Email, Password},
};

#[derive(Debug, <PERSON>lone)]
pub struct AuthenticateUserCommand {
    pub email: String,
    pub password: String,
    pub remember_me: bool,
    pub login_ip: Option<String>,
    pub user_agent: Option<String>,
}

pub struct AuthenticateUserResult {
    pub user_id: String,
    pub session_id: String,
    pub expires_at: std::time::SystemTime,
    pub events: Vec<UserLoggedIn>,
}

pub struct AuthenticateUserHandler<UR, SR>
where
    UR: UserRepository,
    SR: SessionRepository,
{
    user_repository: UR,
    session_repository: SR,
}

impl<UR, SR> AuthenticateUserHandler<UR, SR>
where
    UR: UserRepository,
    SR: SessionRepository,
{
    pub fn new(user_repository: UR, session_repository: SR) -> Self {
        AuthenticateUserHandler {
            user_repository,
            session_repository,
        }
    }

    pub fn handle(
        &mut self,
        command: AuthenticateUserCommand,
    ) -> Result<AuthenticateUserResult, ApplicationError> {
        // Validate email format
        let email = Email::new(&command.email).map_err(ApplicationError::from)?;

        let password = Password::new(&command.password).map_err(ApplicationError::from)?;

        // Find user by email
        let user = self
            .user_repository
            .find_by_email(&email)?
            .ok_or(ApplicationError::Unauthorized)?;

        // Check if user can authenticate (active, not locked, etc.)
        AuthService::can_authenticate(&user).map_err(|_| ApplicationError::Unauthorized)?;

        // Authenticate user
        AuthService::authenticate_user(&user, &password)
            .map_err(|_| ApplicationError::Unauthorized)?;

        // Determine session duration
        let duration = AuthService::determine_session_duration(command.remember_me);

        // Create session
        let session = AuthService::create_session(
            user.id().clone(),
            duration,
            command.login_ip.clone(),
            command.user_agent.clone(),
        );

        let session_id = session.id().clone();
        let expires_at = session.expires_at();

        // Save session
        self.session_repository
            .save(session)
            .map_err(ApplicationError::from)?;

        // Create domain event
        let event = UserLoggedIn::new(
            user.id().clone(),
            session_id.clone(),
            command.login_ip,
            command.user_agent,
            AuthenticationMethod::Password,
        );

        Ok(AuthenticateUserResult {
            user_id: user.id().as_str().to_string(),
            session_id: session_id.as_str().to_string(),
            expires_at: expires_at.into(),
            events: vec![event],
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_domain::entities::{Session, User};
    use auth_domain::errors::DomainError;
    use auth_domain::value_objects::SessionId;
    use auth_domain::value_objects::UserId;

    // Mock repositories for testing
    struct MockUserRepository {
        users: std::collections::HashMap<String, User>,
    }

    impl MockUserRepository {
        fn new() -> Self {
            MockUserRepository {
                users: std::collections::HashMap::new(),
            }
        }

        fn add_user(&mut self, user: User) {
            self.users.insert(user.email().as_str().to_string(), user);
        }
    }

    impl UserRepository for MockUserRepository {
        fn save(&mut self, user: User) -> Result<(), DomainError> {
            self.users.insert(user.email().as_str().to_string(), user);
            Ok(())
        }

        fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError> {
            Ok(self.users.values().find(|u| u.id() == id).cloned())
        }

        fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError> {
            Ok(self.users.get(email.as_str()).cloned())
        }

        fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError> {
            Ok(self.users.contains_key(email.as_str()))
        }

        fn delete(&mut self, _id: &UserId) -> Result<(), DomainError> {
            unimplemented!()
        }

        fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        fn count(&self) -> Result<usize, DomainError> {
            Ok(self.users.len())
        }

        fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        fn find_by_verification_status(
            &self,
            _is_verified: bool,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        fn find_by_active_status(&self, _is_active: bool) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }
    }

    struct MockSessionRepository {
        sessions: std::collections::HashMap<String, Session>,
    }

    impl MockSessionRepository {
        fn new() -> Self {
            MockSessionRepository {
                sessions: std::collections::HashMap::new(),
            }
        }
    }

    impl SessionRepository for MockSessionRepository {
        fn save(&mut self, session: Session) -> Result<(), DomainError> {
            self.sessions
                .insert(session.id().as_str().to_string(), session);
            Ok(())
        }

        fn find_by_id(&self, id: &SessionId) -> Result<Option<Session>, DomainError> {
            Ok(self.sessions.get(id.as_str()).cloned())
        }

        fn find_active_by_user(&self, user_id: &UserId) -> Result<Vec<Session>, DomainError> {
            Ok(self
                .sessions
                .values()
                .filter(|s| s.user_id() == user_id && s.is_valid())
                .cloned()
                .collect())
        }

        fn find_all_by_user(&self, user_id: &UserId) -> Result<Vec<Session>, DomainError> {
            Ok(self
                .sessions
                .values()
                .filter(|s| s.user_id() == user_id)
                .cloned()
                .collect())
        }

        fn delete(&mut self, id: &SessionId) -> Result<(), DomainError> {
            self.sessions.remove(id.as_str());
            Ok(())
        }

        fn delete_all_by_user(&mut self, user_id: &UserId) -> Result<(), DomainError> {
            self.sessions
                .retain(|_, session| session.user_id() != user_id);
            Ok(())
        }

        fn cleanup_expired_sessions(&mut self) -> Result<usize, DomainError> {
            let initial_count = self.sessions.len();
            self.sessions.retain(|_, session| !session.is_expired());
            Ok(initial_count - self.sessions.len())
        }

        fn count_active_by_user(&self, user_id: &UserId) -> Result<usize, DomainError> {
            Ok(self
                .sessions
                .values()
                .filter(|s| s.user_id() == user_id && s.is_valid())
                .count())
        }

        fn find_by_ip_address(&self, ip_address: &str) -> Result<Vec<Session>, DomainError> {
            Ok(self
                .sessions
                .values()
                .filter(|s| s.ip_address() == Some(ip_address))
                .cloned()
                .collect())
        }

        fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<Session>, DomainError> {
            unimplemented!()
        }

        fn update_last_accessed(&mut self, _id: &SessionId) -> Result<(), DomainError> {
            unimplemented!()
        }

        fn invalidate(&mut self, _id: &SessionId) -> Result<(), DomainError> {
            unimplemented!()
        }

        fn invalidate_all_except(
            &mut self,
            _user_id: &UserId,
            _except_session_id: &SessionId,
        ) -> Result<(), DomainError> {
            unimplemented!()
        }
    }

    #[test]
    fn test_authenticate_user_success() {
        let mut user_repo = MockUserRepository::new();
        let session_repo = MockSessionRepository::new();

        // Create and save a test user
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        let user = User::new(email.clone(), password).unwrap();
        user_repo.add_user(user);

        let mut handler = AuthenticateUserHandler::new(user_repo, session_repo);

        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "TestPassword123!".to_string(),
            remember_me: false,
            login_ip: Some("127.0.0.1".to_string()),
            user_agent: Some("Test Agent".to_string()),
        };

        let result = handler.handle(command).unwrap();

        assert!(!result.user_id.is_empty());
        assert!(!result.session_id.is_empty());
        assert_eq!(result.events.len(), 1);
        // Check that we have a UserLoggedIn event
    }

    #[test]
    fn test_authenticate_user_invalid_email() {
        let user_repo = MockUserRepository::new();
        let session_repo = MockSessionRepository::new();
        let mut handler = AuthenticateUserHandler::new(user_repo, session_repo);

        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "TestPassword123!".to_string(),
            remember_me: false,
            login_ip: None,
            user_agent: None,
        };

        let result = handler.handle(command);
        assert!(matches!(result, Err(ApplicationError::Unauthorized)));
    }

    #[test]
    fn test_authenticate_user_wrong_password() {
        let mut user_repo = MockUserRepository::new();
        let session_repo = MockSessionRepository::new();

        // Create and save a test user
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        let mut user = User::new(email, password).unwrap();
        user.change_password("DifferentPassword456!").unwrap();
        user_repo.add_user(user);

        let mut handler = AuthenticateUserHandler::new(user_repo, session_repo);

        let command = AuthenticateUserCommand {
            email: "<EMAIL>".to_string(),
            password: "WrongPassword123!".to_string(),
            remember_me: false,
            login_ip: None,
            user_agent: None,
        };

        let result = handler.handle(command);
        assert!(matches!(result, Err(ApplicationError::Unauthorized)));
    }
}
