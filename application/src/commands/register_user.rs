// User registration command and handler
// Handles the use case of registering a new user

use crate::services::ApplicationError;
use auth_domain::{
    entities::User,
    events::UserRegistered,
    repositories::UserRepository,
    value_objects::{Email, Password},
};

#[derive(Debug, Clone)]
pub struct RegisterUserCommand {
    pub email: String,
    pub password: String,
    pub registration_ip: Option<String>,
    pub user_agent: Option<String>,
}

pub struct RegisterUserResult {
    pub user_id: String,
    pub email: String,
    pub events: Vec<UserRegistered>,
}

pub struct RegisterUserHandler<UR>
where
    UR: UserRepository,
{
    user_repository: UR,
}

impl<UR> RegisterUserHandler<UR>
where
    UR: UserRepository,
{
    pub fn new(user_repository: UR) -> Self {
        RegisterUserHandler { user_repository }
    }

    pub fn handle(
        &mut self,
        command: RegisterUserCommand,
    ) -> Result<RegisterUserResult, ApplicationError> {
        // Validate and create value objects
        let email = Email::new(&command.email).map_err(ApplicationError::from)?;

        let password = Password::new(&command.password).map_err(ApplicationError::from)?;

        // Check if user already exists
        if self.user_repository.exists_by_email(&email)? {
            return Err(ApplicationError::Conflict(
                "User with this email already exists".to_string(),
            ));
        }

        // Create new user
        let user = User::new(email.clone(), password).map_err(ApplicationError::from)?;

        let user_id = user.id().clone();

        // Save user
        self.user_repository
            .save(user)
            .map_err(ApplicationError::from)?;

        // Create domain event
        let event = UserRegistered::new(
            user_id.clone(),
            email.clone(),
            command.registration_ip,
            command.user_agent,
        );

        Ok(RegisterUserResult {
            user_id: user_id.as_str().to_string(),
            email: email.as_str().to_string(),
            events: vec![event],
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_domain::{errors::DomainError, value_objects::UserId};

    // Mock repository for testing
    struct MockUserRepository {
        users: std::collections::HashMap<String, User>,
    }

    impl MockUserRepository {
        fn new() -> Self {
            MockUserRepository {
                users: std::collections::HashMap::new(),
            }
        }
    }

    impl UserRepository for MockUserRepository {
        fn save(&mut self, user: User) -> Result<(), DomainError> {
            self.users.insert(user.email().as_str().to_string(), user);
            Ok(())
        }

        fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError> {
            Ok(self.users.values().find(|u| u.id() == id).cloned())
        }

        fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError> {
            Ok(self.users.get(email.as_str()).cloned())
        }

        fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError> {
            Ok(self.users.contains_key(email.as_str()))
        }

        fn delete(&mut self, _id: &UserId) -> Result<(), DomainError> {
            unimplemented!()
        }

        fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        fn count(&self) -> Result<usize, DomainError> {
            Ok(self.users.len())
        }

        fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        fn find_by_verification_status(
            &self,
            _is_verified: bool,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        fn find_by_active_status(&self, _is_active: bool) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }
    }

    #[test]
    fn test_register_user_success() {
        let mut handler = RegisterUserHandler::new(MockUserRepository::new());

        let command = RegisterUserCommand {
            email: "<EMAIL>".to_string(),
            password: "StrongPassword123!".to_string(),
            registration_ip: Some("127.0.0.1".to_string()),
            user_agent: Some("Test Agent".to_string()),
        };

        let result = handler.handle(command).unwrap();

        assert_eq!(result.email, "<EMAIL>");
        assert!(!result.user_id.is_empty());
        assert_eq!(result.events.len(), 1);
        // Check that we have a UserRegistered event
    }

    #[test]
    fn test_register_user_duplicate_email() {
        let mut repository = MockUserRepository::new();

        // Pre-register a user
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("Password123!").unwrap();
        let mut user = User::new(email, password).unwrap();
        user.change_password("UpdatedPassword789!").unwrap();
        repository.save(user).unwrap();

        let mut handler = RegisterUserHandler::new(repository);

        let command = RegisterUserCommand {
            email: "<EMAIL>".to_string(),
            password: "AnotherPassword123!".to_string(),
            registration_ip: None,
            user_agent: None,
        };

        let result = handler.handle(command);
        assert!(matches!(result, Err(ApplicationError::Conflict(_))));
    }

    #[test]
    fn test_register_user_invalid_email() {
        let mut handler = RegisterUserHandler::new(MockUserRepository::new());

        let command = RegisterUserCommand {
            email: "invalid-email".to_string(),
            password: "StrongPassword123!".to_string(),
            registration_ip: None,
            user_agent: None,
        };

        let result = handler.handle(command);
        assert!(matches!(result, Err(ApplicationError::Domain(_))));
    }

    #[test]
    fn test_register_user_weak_password() {
        let mut handler = RegisterUserHandler::new(MockUserRepository::new());

        let command = RegisterUserCommand {
            email: "<EMAIL>".to_string(),
            password: "weak".to_string(),
            registration_ip: None,
            user_agent: None,
        };

        let result = handler.handle(command);
        assert!(matches!(result, Err(ApplicationError::Domain(_))));
    }
}
