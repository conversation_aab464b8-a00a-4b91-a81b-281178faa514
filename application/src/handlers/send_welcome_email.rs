// Send welcome email event handler
// Handles the UserRegistered event by sending a welcome email

use crate::services::ApplicationError;
use auth_domain::events::UserRegistered;

pub struct SendWelcomeEmailHandler {
    // In a real implementation, this would have an email service dependency
}

impl Default for SendWelcomeEmailHandler {
    fn default() -> Self {
        Self::new()
    }
}

impl SendWelcomeEmailHandler {
    pub fn new() -> Self {
        SendWelcomeEmailHandler {}
    }

    pub fn handle(&self, event: &UserRegistered) -> Result<(), ApplicationError> {
        // In a real implementation, you would:
        // 1. Generate a welcome email template
        // 2. Include verification link if needed
        // 3. Send email via email service (SMTP, SES, etc.)
        // 4. Log the email sending attempt
        // 5. Handle delivery failures

        // For now, we'll just simulate success
        println!(
            "Sending welcome email to {} for user {}",
            event.email.as_str(),
            event.user_id.as_str()
        );

        Ok(())
    }

    /// Generate email content for the welcome message
    #[allow(dead_code)]
    fn generate_welcome_email_content(event: &UserRegistered) -> EmailContent {
        EmailContent {
            to: event.email.as_str().to_string(),
            subject: "Welcome to AuthService!".to_string(),
            body: format!(
                "Hello!\n\nWelcome to AuthService. Your account has been created successfully.\n\nUser ID: {}\nRegistration time: {:?}\n\nBest regards,\nThe AuthService Team",
                event.user_id.as_str(),
                event.occurred_at
            ),
        }
    }
}

#[derive(Debug, Clone)]
#[allow(dead_code)]
struct EmailContent {
    to: String,
    subject: String,
    body: String,
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_domain::value_objects::{Email, UserId};

    #[test]
    fn test_send_welcome_email_handler() {
        let handler = SendWelcomeEmailHandler::new();

        let event = UserRegistered::new(
            UserId::new(),
            Email::new("<EMAIL>").unwrap(),
            Some("127.0.0.1".to_string()),
            Some("Test Agent".to_string()),
        );

        let result = handler.handle(&event);
        assert!(result.is_ok());
    }

    #[test]
    fn test_generate_welcome_email_content() {
        let event = UserRegistered::new(
            UserId::new(),
            Email::new("<EMAIL>").unwrap(),
            None,
            None,
        );

        let content = SendWelcomeEmailHandler::generate_welcome_email_content(&event);

        assert_eq!(content.to, "<EMAIL>");
        assert_eq!(content.subject, "Welcome to AuthService!");
        assert!(content.body.contains("Welcome to AuthService"));
        assert!(content.body.contains(event.user_id.as_str()));
    }
}
