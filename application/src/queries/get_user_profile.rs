// Get user profile query and handler
// Handles the use case of retrieving user profile information

use crate::services::ApplicationError;
use auth_domain::{repositories::UserRepository, value_objects::UserId};

#[derive(Debug, Clone)]
pub struct GetUserProfileQuery {
    pub user_id: String,
    pub requesting_user_id: String,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct UserProfileResult {
    pub user_id: String,
    pub email: String,
    pub is_verified: bool,
    pub is_active: bool,
    pub created_at: std::time::SystemTime,
    pub updated_at: std::time::SystemTime,
}

pub struct GetUserProfileHandler<UR>
where
    UR: UserRepository,
{
    user_repository: UR,
}

impl<UR> GetUserProfileHandler<UR>
where
    UR: UserRepository,
{
    pub fn new(user_repository: UR) -> Self {
        GetUserProfileHandler { user_repository }
    }

    pub fn handle(
        &self,
        query: GetUserProfileQuery,
    ) -> Result<UserProfileResult, ApplicationError> {
        // Validate user IDs
        let user_id = UserId::from_string(query.user_id).map_err(ApplicationError::from)?;

        let requesting_user_id =
            UserId::from_string(query.requesting_user_id).map_err(ApplicationError::from)?;

        // Find the target user
        let user = self
            .user_repository
            .find_by_id(&user_id)?
            .ok_or_else(|| ApplicationError::NotFound("User not found".to_string()))?;

        // Check authorization - users can view their own profile
        // In a real implementation, you would also check if the requesting user has admin rights
        if user_id != requesting_user_id {
            // For now, we'll allow anyone to view any profile
            // In a real implementation, you would check permissions here
            // let requesting_user_roles = get_user_roles(&requesting_user_id)?;
            // if !AuthorizationService::can_access_own_resource(&requesting_user_id, &user_id, &requesting_user_roles) {
            //     return Err(ApplicationError::Forbidden);
            // }
        }

        Ok(UserProfileResult {
            user_id: user.id().as_str().to_string(),
            email: user.email().as_str().to_string(),
            is_verified: user.is_verified(),
            is_active: user.is_active(),
            created_at: user.created_at().into(),
            updated_at: user.updated_at().into(),
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_domain::{
        entities::User,
        errors::DomainError,
        value_objects::{Email, Password},
    };

    // Mock repository for testing
    struct MockUserRepository {
        users: std::collections::HashMap<String, User>,
    }

    impl MockUserRepository {
        fn new() -> Self {
            MockUserRepository {
                users: std::collections::HashMap::new(),
            }
        }

        fn add_user(&mut self, user: User) {
            self.users.insert(user.id().as_str().to_string(), user);
        }
    }

    impl UserRepository for MockUserRepository {
        fn save(&mut self, user: User) -> Result<(), DomainError> {
            self.users.insert(user.id().as_str().to_string(), user);
            Ok(())
        }

        fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError> {
            Ok(self.users.get(id.as_str()).cloned())
        }

        fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError> {
            Ok(self.users.values().find(|u| u.email() == email).cloned())
        }

        fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError> {
            Ok(self.users.values().any(|u| u.email() == email))
        }

        fn delete(&mut self, _id: &UserId) -> Result<(), DomainError> {
            unimplemented!()
        }

        fn find_all(&self, _offset: usize, _limit: usize) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        fn count(&self) -> Result<usize, DomainError> {
            Ok(self.users.len())
        }

        fn find_by_created_date_range(
            &self,
            _start: std::time::SystemTime,
            _end: std::time::SystemTime,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        fn find_by_verification_status(
            &self,
            _is_verified: bool,
        ) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }

        fn find_by_active_status(&self, _is_active: bool) -> Result<Vec<User>, DomainError> {
            unimplemented!()
        }
    }

    #[test]
    fn test_get_user_profile_success() {
        let mut repository = MockUserRepository::new();

        // Create and add a test user
        let email = Email::new("<EMAIL>").unwrap();
        let password = Password::new("TestPassword123!").unwrap();
        let mut user = User::new(email, password).unwrap();
        user.change_password("UniquePassword987!").unwrap();
        let user_id = user.id().clone();
        repository.add_user(user);

        let handler = GetUserProfileHandler::new(repository);

        let query = GetUserProfileQuery {
            user_id: user_id.as_str().to_string(),
            requesting_user_id: user_id.as_str().to_string(), // Same user
        };

        let result = handler.handle(query).unwrap();

        assert_eq!(result.email, "<EMAIL>");
        assert!(!result.is_verified); // New users are not verified
        assert!(result.is_active); // New users are active
    }

    #[test]
    fn test_get_user_profile_not_found() {
        let repository = MockUserRepository::new();
        let handler = GetUserProfileHandler::new(repository);

        let query = GetUserProfileQuery {
            user_id: "nonexistent_user".to_string(),
            requesting_user_id: "requesting_user".to_string(),
        };

        let result = handler.handle(query);
        assert!(matches!(result, Err(ApplicationError::NotFound(_))));
    }

    #[test]
    fn test_get_user_profile_invalid_user_id() {
        let repository = MockUserRepository::new();
        let handler = GetUserProfileHandler::new(repository);

        let query = GetUserProfileQuery {
            user_id: "".to_string(),
            requesting_user_id: "requesting_user".to_string(),
        };

        let result = handler.handle(query);
        assert!(matches!(result, Err(ApplicationError::Domain(_))));
    }
}
