// Application service coordination
// Coordinates between different application services

use auth_domain::errors::DomainError;

/// Main application service that coordinates use cases
pub struct ApplicationService<UR, SR>
where
    UR: auth_domain::repositories::UserRepository,
    SR: auth_domain::repositories::SessionRepository,
{
    user_repository: UR,
    session_repository: SR,
}

impl<UR, SR> ApplicationService<UR, SR>
where
    UR: auth_domain::repositories::UserRepository,
    SR: auth_domain::repositories::SessionRepository,
{
    pub fn new(user_repository: UR, session_repository: SR) -> Self {
        ApplicationService {
            user_repository,
            session_repository,
        }
    }

    pub fn user_repository(&self) -> &UR {
        &self.user_repository
    }

    pub fn user_repository_mut(&mut self) -> &mut UR {
        &mut self.user_repository
    }

    pub fn session_repository(&self) -> &SR {
        &self.session_repository
    }

    pub fn session_repository_mut(&mut self) -> &mut SR {
        &mut self.session_repository
    }
}

/// Application-level error types
#[derive(Debug, Clone)]
pub enum ApplicationError {
    Domain(DomainError),
    ValidationFailed(String),
    NotFound(String),
    Conflict(String),
    Unauthorized,
    Forbidden,
}

impl std::fmt::Display for ApplicationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ApplicationError::Domain(err) => write!(f, "Domain error: {err}"),
            ApplicationError::ValidationFailed(msg) => write!(f, "Validation failed: {msg}"),
            ApplicationError::NotFound(msg) => write!(f, "Not found: {msg}"),
            ApplicationError::Conflict(msg) => write!(f, "Conflict: {msg}"),
            ApplicationError::Unauthorized => write!(f, "Unauthorized"),
            ApplicationError::Forbidden => write!(f, "Forbidden"),
        }
    }
}

impl std::error::Error for ApplicationError {}

impl From<DomainError> for ApplicationError {
    fn from(err: DomainError) -> Self {
        ApplicationError::Domain(err)
    }
}
