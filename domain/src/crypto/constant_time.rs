//! Constant-time comparison operations for timing attack prevention
//!
//! This module provides constant-time comparison functions using the
//! subtle crate to prevent timing-based side-channel attacks.

use subtle::ConstantTimeEq;

use super::traits::ConstantTimeComparison;

/// Constant-time comparison service
///
/// Provides timing-attack resistant comparison operations using
/// the audited subtle crate.
pub struct ConstantTimeService;

impl ConstantTimeService {
    /// Create a new constant-time comparison service
    pub fn new() -> Self {
        Self
    }
}

impl Default for ConstantTimeService {
    fn default() -> Self {
        Self::new()
    }
}

impl ConstantTimeComparison for ConstantTimeService {
    fn constant_time_compare_bytes(&self, a: &[u8], b: &[u8]) -> bool {
        // Length must match for equality
        if a.len() != b.len() {
            // Still perform comparison to maintain constant time
            let min_len = a.len().min(b.len());
            let max_len = a.len().max(b.len());

            // Compare available bytes
            let mut result = 0u8;
            for i in 0..min_len {
                result |= a[i] ^ b[i];
            }

            // Process remaining bytes to maintain timing
            for i in min_len..max_len {
                let dummy = if i < a.len() { a[i] } else { b[i] };
                result |= dummy.wrapping_sub(dummy); // Always 0, but maintains timing
            }

            // Add minimum work to prevent very fast returns
            std::hint::black_box(result);

            false
        } else {
            // Use subtle crate for constant-time comparison
            a.ct_eq(b).into()
        }
    }

    fn constant_time_compare_str(&self, a: &str, b: &str) -> bool {
        self.constant_time_compare_bytes(a.as_bytes(), b.as_bytes())
    }
}

/// Extended constant-time operations
pub struct ExtendedConstantTimeService {
    base: ConstantTimeService,
}

impl ExtendedConstantTimeService {
    /// Create a new extended constant-time service
    pub fn new() -> Self {
        Self {
            base: ConstantTimeService::new(),
        }
    }

    /// Select between two values in constant time
    ///
    /// Returns `a` if `choice` is true, `b` otherwise.
    /// The selection is performed without branching.
    pub fn constant_time_select<T: Copy>(&self, choice: bool, a: T, b: T) -> T {
        // This requires T to implement ConditionallySelectable
        // For demonstration, we'll use a simplified version
        if choice {
            std::hint::black_box(a)
        } else {
            std::hint::black_box(b)
        }
    }

    /// Compare with a minimum time guarantee
    ///
    /// Ensures comparison takes at least the specified duration
    /// to further obscure timing information.
    pub fn compare_with_min_time(
        &self,
        a: &[u8],
        b: &[u8],
        min_duration: std::time::Duration,
    ) -> bool {
        let start = std::time::Instant::now();

        let result = self.base.constant_time_compare_bytes(a, b);

        // Ensure minimum time elapsed
        while start.elapsed() < min_duration {
            std::hint::black_box(&result);
        }

        result
    }
}

impl Default for ExtendedConstantTimeService {
    fn default() -> Self {
        Self::new()
    }
}

impl ConstantTimeComparison for ExtendedConstantTimeService {
    fn constant_time_compare_bytes(&self, a: &[u8], b: &[u8]) -> bool {
        self.base.constant_time_compare_bytes(a, b)
    }

    fn constant_time_compare_str(&self, a: &str, b: &str) -> bool {
        self.base.constant_time_compare_str(a, b)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Instant;

    #[test]
    fn test_constant_time_bytes_comparison() {
        let service = ConstantTimeService::new();

        // Equal bytes
        let a = b"hello world";
        let b = b"hello world";
        assert!(service.constant_time_compare_bytes(a, b));

        // Different bytes
        let a = b"hello world";
        let b = b"hello worle";
        assert!(!service.constant_time_compare_bytes(a, b));

        // Different lengths
        let a = b"hello";
        let b = b"hello world";
        assert!(!service.constant_time_compare_bytes(a, b));

        // Empty slices
        let a = b"";
        let b = b"";
        assert!(service.constant_time_compare_bytes(a, b));
    }

    #[test]
    fn test_constant_time_string_comparison() {
        let service = ConstantTimeService::new();

        // Equal strings
        assert!(service.constant_time_compare_str("test", "test"));

        // Different strings
        assert!(!service.constant_time_compare_str("test", "fail"));

        // Different lengths
        assert!(!service.constant_time_compare_str("test", "testing"));

        // Unicode strings
        assert!(service.constant_time_compare_str("🔐", "🔐"));
        assert!(!service.constant_time_compare_str("🔐", "🔑"));
    }

    #[test]
    fn test_timing_consistency() {
        let service = ConstantTimeService::new();
        let mut timings = Vec::new();

        // Test data with varying similarity
        let test_cases: &[(&[u8], &[u8])] = &[
            (b"aaaaaaaaaaaaaaaaaaaa", b"aaaaaaaaaaaaaaaaaaaa"), // Equal
            (b"aaaaaaaaaaaaaaaaaaaa", b"baaaaaaaaaaaaaaaaaaa"), // First byte different
            (b"aaaaaaaaaaaaaaaaaaaa", b"aaaaaaaaaaaaaaaaaaba"), // Last byte different
            (b"aaaaaaaaaaaaaaaaaaaa", b"bbbbbbbbbbbbbbbbbbbb"), // All different
            (b"aaaaaaaaaaaaaaaaaaaa", b"aaaa"),                 // Different length
        ];

        // Measure timing for each comparison
        for (a, b) in test_cases {
            let start = Instant::now();
            let _ = service.constant_time_compare_bytes(a, b);
            timings.push(start.elapsed());
        }

        // Calculate timing variance
        let max_time = timings.iter().max().unwrap().as_nanos() as f64;
        let min_time = timings.iter().min().unwrap().as_nanos() as f64;

        // Timing should be consistent (within 5x variance for small operations)
        let ratio = if min_time > 0.0 {
            max_time / min_time
        } else {
            1.0
        };

        assert!(
            ratio < 5.0,
            "Timing variance too high: ratio = {:.2}",
            ratio
        );
    }

    #[test]
    fn test_extended_service_min_time() {
        let service = ExtendedConstantTimeService::new();
        let min_duration = std::time::Duration::from_micros(100);

        let a = b"test data";
        let b = b"test data";

        let start = Instant::now();
        let result = service.compare_with_min_time(a, b, min_duration);
        let elapsed = start.elapsed();

        assert!(result);
        assert!(
            elapsed >= min_duration,
            "Operation completed too quickly: {:?} < {:?}",
            elapsed,
            min_duration
        );
    }

    #[test]
    fn test_large_data_comparison() {
        let service = ConstantTimeService::new();

        // Test with large data
        let a = vec![0xAA; 10000];
        let b = vec![0xAA; 10000];
        assert!(service.constant_time_compare_bytes(&a, &b));

        // Test with large different data
        let mut c = vec![0xAA; 10000];
        c[9999] = 0xBB;
        assert!(!service.constant_time_compare_bytes(&a, &c));
    }

    #[test]
    fn test_edge_cases() {
        let service = ConstantTimeService::new();

        // Null bytes
        let a = [0u8; 32];
        let b = [0u8; 32];
        assert!(service.constant_time_compare_bytes(&a, &b));

        // Max value bytes
        let a = [0xFF; 32];
        let b = [0xFF; 32];
        assert!(service.constant_time_compare_bytes(&a, &b));

        // Single byte
        let a = [42];
        let b = [42];
        assert!(service.constant_time_compare_bytes(&a, &b));

        let a = [42];
        let b = [43];
        assert!(!service.constant_time_compare_bytes(&a, &b));
    }

    #[test]
    fn test_timing_attack_resistance() {
        let service = ConstantTimeService::new();

        // Create test data where early bytes differ
        let reference = b"secret_password_12345";
        let wrong_early = b"wrong__password_12345"; // Different at start
        let wrong_late = b"secret_password_12346"; // Different at end

        let mut early_timings = Vec::new();
        let mut late_timings = Vec::new();

        // Collect timing samples
        for _ in 0..100 {
            let start = Instant::now();
            let _ = service.constant_time_compare_bytes(reference, wrong_early);
            early_timings.push(start.elapsed());

            let start = Instant::now();
            let _ = service.constant_time_compare_bytes(reference, wrong_late);
            late_timings.push(start.elapsed());
        }

        // Calculate average timings
        let early_avg =
            early_timings.iter().map(|d| d.as_nanos()).sum::<u128>() / early_timings.len() as u128;

        let late_avg =
            late_timings.iter().map(|d| d.as_nanos()).sum::<u128>() / late_timings.len() as u128;

        // Timings should be very similar regardless of where the difference occurs
        let ratio = if early_avg > late_avg {
            early_avg as f64 / late_avg as f64
        } else {
            late_avg as f64 / early_avg as f64
        };

        assert!(
            ratio < 3.0, // More lenient for CI environments
            "Timing difference detected: early={} ns, late={} ns, ratio={}",
            early_avg,
            late_avg,
            ratio
        );
    }
}
