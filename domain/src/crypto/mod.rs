//! Cryptographic services module for AuthService domain layer
//!
//! This module provides secure, audited cryptographic operations using
//! approved libraries as mandated by CRYPTOGRAPHIC-POLICY.md.
//!
//! # Security Requirements
//!
//! - All cryptographic operations MUST use approved, audited libraries
//! - NO custom cryptographic implementations are allowed
//! - All operations must be timing-attack resistant
//! - Follow OWASP 2025 security guidelines
//!
//! # Approved Libraries
//!
//! - `argon2`: Password hashing (Argon2id, RFC 9106)
//! - `rand` + `rand_chacha`: Secure random generation
//! - `subtle`: Constant-time operations
//! - `base64`: Encoding/decoding
//! - `zeroize`: Secure memory wiping
//!
//! # Usage
//!
//! ```rust
//! use auth_domain::crypto::{CryptoService, UnifiedCryptoService};
//!
//! let crypto = UnifiedCryptoService::new()?;
//!
//! // Password operations
//! let hash = crypto.hash_password("SecurePassword123!")?;
//! let valid = crypto.verify_password("SecurePassword123!", &hash)?;
//!
//! // Random generation
//! let token = crypto.generate_session_token();
//! let salt = crypto.generate_salt();
//!
//! // Constant-time comparison
//! let equal = crypto.constant_time_compare_str("secret1", "secret2");
//! ```

// Public traits defining cryptographic interfaces
pub mod traits;

// Implementation modules
mod constant_time;
mod password_service;
mod random_service;
mod service;

// Re-export main types and traits
pub use traits::{
    ConstantTimeComparison, CryptoError, CryptoService, PasswordHashingService, SecureRandomService,
};

pub use constant_time::{ConstantTimeService, ExtendedConstantTimeService};
pub use password_service::{Argon2PasswordService, SecurePassword};
pub use random_service::{ChaChaRandomService, OsRandomService, SecureBuffer};
pub use service::{CryptoServiceFactory, UnifiedCryptoService};

/// Create the default production crypto service
///
/// This is the recommended way to create a crypto service for production use.
/// It uses OWASP 2025 compliant parameters and approved libraries.
///
/// # Example
///
/// ```rust
/// use auth_domain::crypto;
///
/// let service = crypto::create_default().unwrap();
/// let token = service.generate_session_token();
/// ```
pub fn create_default() -> Result<UnifiedCryptoService, CryptoError> {
    UnifiedCryptoService::new()
}

/// Module-level security tests
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_module_exports() {
        // Verify all main types are accessible
        let _service = UnifiedCryptoService::new().unwrap();
        let _password = Argon2PasswordService::new().unwrap();
        let _random = ChaChaRandomService::new();
        let _constant = ConstantTimeService::new();
    }

    #[test]
    fn test_create_default() {
        let service = create_default().unwrap();

        // Test basic operations
        let salt = service.generate_salt();
        assert_eq!(salt.len(), 32);

        let token = service.generate_session_token();
        assert!(token.starts_with("sess_"));
    }

    #[test]
    fn test_trait_implementations() {
        let service = create_default().unwrap();

        // Test PasswordHashingService
        let password = "TestPassword123!@#";
        let hash = service.hash_password(password).unwrap();
        assert!(service.verify_password(password, &hash).unwrap());

        // Test SecureRandomService
        let bytes = service.generate_secure_bytes(16);
        assert_eq!(bytes.len(), 16);

        // Test ConstantTimeComparison
        assert!(service.constant_time_compare_str("test", "test"));
        assert!(!service.constant_time_compare_str("test", "fail"));

        // Test CryptoService
        let csrf = service.generate_csrf_token();
        assert!(!csrf.is_empty());
    }

    #[test]
    fn test_error_handling() {
        let service = create_default().unwrap();

        // Test invalid password
        let result = service.hash_password("short");
        assert!(result.is_err());

        // Test invalid hash format
        let result = service.verify_password("password", "invalid-hash");
        assert!(result.is_err());
    }

    #[test]
    fn test_security_compliance() {
        let service = create_default().unwrap();
        let password = "CompliantPassword123!@#";

        // Verify Argon2id parameters in hash
        let hash = service.hash_password(password).unwrap();
        assert!(hash.starts_with("$argon2id$"));
        assert!(hash.contains("m=19456")); // OWASP 2025 memory parameter
        assert!(hash.contains("t=2")); // OWASP 2025 iterations
        assert!(hash.contains("p=1")); // OWASP 2025 parallelism
    }
}
