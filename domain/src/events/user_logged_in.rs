// User logged in event
// Fired when a user successfully authenticates

// use super::DomainEvent;
use crate::value_objects::SessionId;
use crate::value_objects::UserId;

#[derive(Debug, Clone)]
pub struct UserLoggedIn {
    pub user_id: UserId,
    pub session_id: SessionId,
    pub occurred_at: std::time::SystemTime,
    pub login_ip: Option<String>,
    pub user_agent: Option<String>,
    pub authentication_method: AuthenticationMethod,
}

#[derive(Debug, Clone, PartialEq)]
pub enum AuthenticationMethod {
    Password,
    Totp,
    WebAuthn,
    OAuth(String), // Provider name
    Saml(String),  // Provider name
}

impl UserLoggedIn {
    pub fn new(
        user_id: UserId,
        session_id: SessionId,
        login_ip: Option<String>,
        user_agent: Option<String>,
        authentication_method: AuthenticationMethod,
    ) -> Self {
        UserLoggedIn {
            user_id,
            session_id,
            occurred_at: std::time::SystemTime::now(),
            login_ip,
            user_agent,
            authentication_method,
        }
    }
}

/* impl DomainEvent for UserLoggedIn {
    fn event_type(&self) -> &'static str {
        "user.logged_in"
    }

    fn occurred_at(&self) -> std::time::SystemTime {
        self.occurred_at
    }

    fn aggregate_id(&self) -> String {
        self.user_id.as_str().to_string()
    }
} */
