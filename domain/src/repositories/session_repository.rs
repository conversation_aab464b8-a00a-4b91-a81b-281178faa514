// Session repository trait
// Defines the persistence interface for Session entities

use crate::entities::Session;
use crate::errors::DomainError;
use crate::value_objects::SessionId;
use crate::value_objects::UserId;

/// Repository trait for Session entity
/// This is a port in hexagonal architecture - implementations are adapters
pub trait SessionRepository {
    /// Save a new session or update an existing one
    fn save(&mut self, session: Session) -> Result<(), DomainError>;

    /// Find a session by its ID
    fn find_by_id(&self, id: &SessionId) -> Result<Option<Session>, DomainError>;

    /// Find all active sessions for a user
    fn find_active_by_user(&self, user_id: &UserId) -> Result<Vec<Session>, DomainError>;

    /// Find all sessions for a user (active and inactive)
    fn find_all_by_user(&self, user_id: &UserId) -> Result<Vec<Session>, DomainError>;

    /// Delete a session by its ID
    fn delete(&mut self, id: &SessionId) -> Result<(), DomainError>;

    /// Delete all sessions for a user
    fn delete_all_by_user(&mut self, user_id: &UserId) -> Result<(), DomainError>;

    /// Find and delete expired sessions
    fn cleanup_expired_sessions(&mut self) -> Result<usize, DomainError>;

    /// Count active sessions for a user
    fn count_active_by_user(&self, user_id: &UserId) -> Result<usize, DomainError>;

    /// Find sessions by IP address (for security analysis)
    fn find_by_ip_address(&self, ip_address: &str) -> Result<Vec<Session>, DomainError>;

    /// Find sessions created within a date range
    fn find_by_created_date_range(
        &self,
        start: std::time::SystemTime,
        end: std::time::SystemTime,
    ) -> Result<Vec<Session>, DomainError>;

    /// Update session last accessed time
    fn update_last_accessed(&mut self, id: &SessionId) -> Result<(), DomainError>;

    /// Invalidate a session (mark as inactive)
    fn invalidate(&mut self, id: &SessionId) -> Result<(), DomainError>;

    /// Invalidate all sessions for a user except the given session
    fn invalidate_all_except(
        &mut self,
        user_id: &UserId,
        except_session_id: &SessionId,
    ) -> Result<(), DomainError>;
}
