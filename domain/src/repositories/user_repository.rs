// User repository trait
// Defines the persistence interface for User entities

use crate::entities::User;
use crate::errors::DomainError;
use crate::value_objects::{Email, UserId};

/// Repository trait for User aggregate
/// This is a port in hexagonal architecture - implementations are adapters
pub trait UserRepository {
    /// Save a new user or update an existing one
    fn save(&mut self, user: User) -> Result<(), DomainError>;

    /// Find a user by their ID
    fn find_by_id(&self, id: &UserId) -> Result<Option<User>, DomainError>;

    /// Find a user by their email address
    fn find_by_email(&self, email: &Email) -> Result<Option<User>, DomainError>;

    /// Check if a user exists with the given email
    fn exists_by_email(&self, email: &Email) -> Result<bool, DomainError>;

    /// Delete a user by their ID
    fn delete(&mut self, id: &UserId) -> Result<(), DomainError>;

    /// Get all users (paginated)
    fn find_all(&self, offset: usize, limit: usize) -> Result<Vec<User>, DomainError>;

    /// Count total number of users
    fn count(&self) -> Result<usize, DomainError>;

    /// Find users created within a date range
    fn find_by_created_date_range(
        &self,
        start: std::time::SystemTime,
        end: std::time::SystemTime,
    ) -> Result<Vec<User>, DomainError>;

    /// Find users by verification status
    fn find_by_verification_status(&self, is_verified: bool) -> Result<Vec<User>, DomainError>;

    /// Find users by active status
    fn find_by_active_status(&self, is_active: bool) -> Result<Vec<User>, DomainError>;
}
