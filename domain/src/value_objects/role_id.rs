//! Role ID Value Object
//!
//! Provides a strongly-typed identifier for roles to prevent ID confusion
//! and improve type safety across the authorization domain.

use crate::crypto::{ChaChaRandomService, SecureRandomService};
use crate::errors::DomainError;
use std::fmt::{<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Result as FmtR<PERSON><PERSON>};

/// Strongly-typed role identifier
///
/// Roles are represented by unique identifiers that distinguish between
/// different permission sets. This type ensures role IDs cannot be
/// confused with other ID types.
#[derive(Debug, Clone, PartialEq, Eq, Hash, PartialOrd, Ord)]
pub struct RoleId(String);

impl RoleId {
    /// Generate a new role ID
    ///
    /// Creates a new role ID with secure random generation to ensure
    /// uniqueness across the system.
    pub fn generate() -> Self {
        let random_service = ChaChaRandomService::new();
        let random_bytes = random_service.generate_secure_bytes(16);
        let hex_id = random_bytes
            .iter()
            .map(|b| format!("{:02x}", b))
            .collect::<String>();
        RoleId(format!("role_{}", hex_id))
    }

    /// Create a RoleId from a string (used when loading from storage)
    ///
    /// # Arguments
    ///
    /// * `id` - The role ID string from storage
    ///
    /// # Returns
    ///
    /// * `Ok(RoleId)` if the ID format is valid
    /// * `Err(DomainError)` if the format is invalid
    pub fn from_string(id: String) -> Result<Self, DomainError> {
        if id.is_empty() {
            return Err(DomainError::InvalidId("RoleId cannot be empty".to_string()));
        }

        // Basic format validation
        if !id.starts_with("role_") {
            return Err(DomainError::InvalidId(
                "RoleId must start with 'role_'".to_string(),
            ));
        }

        // Check length - should have prefix + at least 32 hex chars
        if id.len() < 37 {
            return Err(DomainError::InvalidId("RoleId is too short".to_string()));
        }

        // Validate hex characters after prefix
        let hex_part = &id[5..]; // Skip "role_"
        if !hex_part.chars().all(|c| c.is_ascii_hexdigit()) {
            return Err(DomainError::InvalidId(
                "RoleId contains invalid characters".to_string(),
            ));
        }

        Ok(RoleId(id))
    }

    /// Create a RoleId for built-in system roles
    ///
    /// Used for creating predefined roles like "admin", "user", etc.
    /// These have a different format to distinguish them from generated IDs.
    pub fn builtin(name: &str) -> Result<Self, DomainError> {
        if name.is_empty() {
            return Err(DomainError::InvalidId(
                "Role name cannot be empty".to_string(),
            ));
        }

        // Validate name contains only alphanumeric and underscore
        if !name.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(DomainError::InvalidId(
                "Role name can only contain alphanumeric characters and underscores".to_string(),
            ));
        }

        Ok(RoleId(format!("role_builtin_{}", name.to_lowercase())))
    }

    /// Get the role ID as a string
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Check if this is a built-in system role
    pub fn is_builtin(&self) -> bool {
        self.0.starts_with("role_builtin_")
    }

    /// Get the role name for built-in roles
    ///
    /// Returns None for generated role IDs
    pub fn builtin_name(&self) -> Option<&str> {
        if self.is_builtin() {
            Some(&self.0[13..]) // Skip "role_builtin_"
        } else {
            None
        }
    }
}

impl Display for RoleId {
    fn fmt(&self, f: &mut Formatter<'_>) -> FmtResult {
        write!(f, "{}", self.0)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_role_id() {
        let id1 = RoleId::generate();
        let id2 = RoleId::generate();

        // IDs should be different
        assert_ne!(id1, id2);

        // Should have correct prefix
        assert!(id1.as_str().starts_with("role_"));
        assert!(id2.as_str().starts_with("role_"));

        // Should not be built-in
        assert!(!id1.is_builtin());
        assert!(!id2.is_builtin());

        // Should have sufficient length
        assert!(id1.as_str().len() >= 37);
        assert!(id2.as_str().len() >= 37);
    }

    #[test]
    fn test_from_string_valid() {
        let id_str = "role_abcdef1234567890abcdef1234567890abcdef12".to_string();
        let role_id = RoleId::from_string(id_str.clone()).unwrap();
        assert_eq!(role_id.as_str(), id_str);
    }

    #[test]
    fn test_from_string_invalid() {
        // Empty string
        assert!(RoleId::from_string("".to_string()).is_err());

        // Wrong prefix
        assert!(RoleId::from_string("user_123456".to_string()).is_err());

        // Too short
        assert!(RoleId::from_string("role_123".to_string()).is_err());

        // Invalid hex characters
        assert!(RoleId::from_string("role_gggggggggggggggggggggggggggggggg".to_string()).is_err());
    }

    #[test]
    fn test_builtin_role() {
        let admin_role = RoleId::builtin("admin").unwrap();
        assert_eq!(admin_role.as_str(), "role_builtin_admin");
        assert!(admin_role.is_builtin());
        assert_eq!(admin_role.builtin_name(), Some("admin"));

        let user_role = RoleId::builtin("user").unwrap();
        assert_eq!(user_role.as_str(), "role_builtin_user");
        assert!(user_role.is_builtin());
        assert_eq!(user_role.builtin_name(), Some("user"));
    }

    #[test]
    fn test_builtin_role_invalid() {
        // Empty name
        assert!(RoleId::builtin("").is_err());

        // Invalid characters
        assert!(RoleId::builtin("admin@role").is_err());
        assert!(RoleId::builtin("admin-role").is_err());
        assert!(RoleId::builtin("admin role").is_err());
    }

    #[test]
    fn test_generated_role_not_builtin() {
        let generated = RoleId::generate();
        assert!(!generated.is_builtin());
        assert!(generated.builtin_name().is_none());
    }

    #[test]
    fn test_display() {
        let id = RoleId::generate();
        let display_str = format!("{}", id);
        assert_eq!(display_str, id.as_str());
    }

    #[test]
    fn test_ordering() {
        let id1 = RoleId::from_string("role_aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa".to_string())
            .unwrap();
        let id2 = RoleId::from_string("role_bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb".to_string())
            .unwrap();

        assert!(id1 < id2);
    }
}
