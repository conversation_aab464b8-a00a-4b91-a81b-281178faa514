[package]
name = "auth-infrastructure"
version = "0.1.0"
edition.workspace = true

[dependencies]
auth-domain = { path = "../domain" }
auth-application = { path = "../application" }

# External dependencies from workspace
tokio = { workspace = true }
serde = { workspace = true }
serde_json = { workspace = true }
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
anyhow = { workspace = true }
uuid = { workspace = true }
chrono = { workspace = true }
rand = { workspace = true }

# Configuration
figment = { workspace = true }

# Security
argon2 = { workspace = true }
jsonwebtoken = { workspace = true }
otpauth = { workspace = true }
webauthn-rs = { workspace = true }
ring = { workspace = true }
thiserror = { workspace = true }
base32 = { workspace = true }

# Database
sqlx = { workspace = true }
libsql = { workspace = true }

# Cache (using redis crate for Valkey/Redis connectivity)
redis = { workspace = true }

# HTTP client
reqwest = { workspace = true }

# gRPC
tonic = { workspace = true }
prost = { workspace = true }

# Observability
opentelemetry = { workspace = true }
opentelemetry-jaeger = { workspace = true }

# Statistics
statrs = { workspace = true }

# File watching
notify = { workspace = true }