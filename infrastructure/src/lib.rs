// Infrastructure crate - External adapters and implementations
// This crate contains adapters that implement domain ports for external systems

pub mod adapters;
pub mod configuration;
pub mod migrations;

// Re-export commonly used adapters
pub use adapters::cache::RedisCache;
pub use adapters::database::{SqlxSessionRepository, SqlxUserRepository};
pub use adapters::email::SmtpEmailService;
pub use adapters::security::{JwtTokenService, TotpService};
pub use configuration::Settings;
